#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Discogs API Release 数据补全器

功能：
1. 检测数据库中缺失的release ID
2. 通过Discogs API获取缺失的数据
3. 转换API响应为数据库兼容格式
4. 批量插入到release_new集合

特性：
- 进度跟踪和断点续传
- API频率限制（1秒1次）
- 错误处理和重试机制
- 批量数据库操作优化
- 详细的日志记录

作者：AI Assistant
创建时间：2025-07-28
"""

import os
import sys
import time
import json
import csv
import requests
import logging
from datetime import datetime, timezone
from pymongo import MongoClient
from typing import Set, List, Dict, Optional
import signal

# 导入现有的枚举类
try:
    from release.enums import Permissions, Status, Source
except ImportError:
    # 如果导入失败，定义本地枚举
    class Permissions:
        ALL_VISIBLE = 1
    class Status:
        ACTIVE = 1
    class Source:
        DISCOGS = 1

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
COLLECTION_NAME = 'release_new'

# API配置
API_BASE_URL = 'https://api.discogs.com/releases'
API_RATE_LIMIT = 1.0  # 1秒1次请求
API_TIMEOUT = 30  # 请求超时时间
MAX_RETRIES = 3  # 最大重试次数

# 处理配置
BATCH_SIZE = int(os.getenv('BATCH_SIZE', '100'))  # 批量插入大小
MAX_RECORDS = int(os.getenv('MAX_RECORDS', '0'))  # 最大处理记录数，0表示全部
START_ID = int(os.getenv('START_ID', '1'))  # 开始处理的ID
TEST_MODE = os.getenv('TEST_MODE', 'false').lower() == 'true'  # 测试模式

# 分阶段处理配置
MAX_SEGMENTS = int(os.getenv('MAX_SEGMENTS', '10'))  # 最大段数（优先使用）
SEGMENT_SIZE = int(os.getenv('SEGMENT_SIZE', '100000'))  # 每段检测的ID数量（备选方案）
PROCESSING_BATCH_SIZE = int(os.getenv('PROCESSING_BATCH_SIZE', '5000'))  # 每批次处理的缺失ID数量
MAX_CONCURRENT_BATCHES = int(os.getenv('MAX_CONCURRENT_BATCHES', '1'))  # 最大并发批次数

# 文件路径
PROGRESS_FILE = 'api_补全_progress.json'
LOG_FILE = 'api_补全_log.txt'
ERROR_LOG_FILE = 'api_补全_errors.txt'
CSV_OUTPUT_FILE = 'api_补全_releases.csv'

# 分阶段处理文件路径
MISSING_IDS_FILE = 'missing_ids_segments.json'
BATCH_CONFIG_FILE = 'batch_config.json'
BATCH_PROGRESS_DIR = 'batch_progress'
SEGMENT_PROGRESS_FILE = 'segment_progress.json'

# 404 ID管理文件路径
NOT_FOUND_IDS_FILE = 'api_404_ids.json'

# 全局变量
should_stop = False
not_found_ids_set = set()  # 存储404 ID的集合

def signal_handler(signum, frame):
    """信号处理器，用于优雅停止"""
    global should_stop
    should_stop = True
    print("\n🛑 接收到停止信号，正在安全退出...")

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self):
        self.processed = 0
        self.successful = 0
        self.skipped_404 = 0
        self.errors = 0
        self.start_time = time.time()
        self.last_id = START_ID
        self.load_progress()
    
    def load_progress(self):
        """加载进度"""
        if os.path.exists(PROGRESS_FILE):
            try:
                with open(PROGRESS_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.processed = data.get('processed', 0)
                    self.successful = data.get('successful', 0)
                    self.skipped_404 = data.get('skipped_404', 0)
                    self.errors = data.get('errors', 0)
                    self.last_id = data.get('last_id', START_ID)
                    logger.info(f"📂 加载进度: 已处理 {self.processed}, 成功 {self.successful}, 跳过 {self.skipped_404}, 错误 {self.errors}")
                    logger.info(f"📍 从ID {self.last_id} 继续处理")
            except Exception as e:
                logger.warning(f"⚠️ 加载进度失败: {e}")
    
    def save_progress(self):
        """保存进度"""
        try:
            data = {
                'processed': self.processed,
                'successful': self.successful,
                'skipped_404': self.skipped_404,
                'errors': self.errors,
                'last_id': self.last_id,
                'timestamp': datetime.now().isoformat()
            }
            with open(PROGRESS_FILE, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"❌ 保存进度失败: {e}")
    
    def update(self, result_type: str, release_id: int):
        """更新进度"""
        self.processed += 1
        self.last_id = release_id
        
        if result_type == 'success':
            self.successful += 1
        elif result_type == '404':
            self.skipped_404 += 1
        elif result_type == 'error':
            self.errors += 1
        
        # 每处理100条记录保存一次进度
        if self.processed % 100 == 0:
            self.save_progress()
    
    def get_stats(self) -> Dict:
        """获取统计信息"""
        elapsed = time.time() - self.start_time
        rate = self.processed / elapsed if elapsed > 0 else 0
        
        return {
            'processed': self.processed,
            'successful': self.successful,
            'skipped_404': self.skipped_404,
            'errors': self.errors,
            'elapsed_time': elapsed,
            'processing_rate': rate,
            'success_rate': (self.successful / self.processed * 100) if self.processed > 0 else 0
        }

class DiscogsAPIClient:
    """Discogs API客户端"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'DiscogsReleaseCompleter/1.0 +https://example.com/contact'
        })
        self.last_request_time = 0
    
    def _wait_for_rate_limit(self):
        """等待满足API频率限制"""
        elapsed = time.time() - self.last_request_time
        if elapsed < API_RATE_LIMIT:
            time.sleep(API_RATE_LIMIT - elapsed)
    
    def get_release(self, release_id: int) -> Optional[Dict]:
        """获取release数据"""
        self._wait_for_rate_limit()
        
        url = f"{API_BASE_URL}/{release_id}"
        
        for attempt in range(MAX_RETRIES):
            try:
                self.last_request_time = time.time()
                response = self.session.get(url, timeout=API_TIMEOUT)
                
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 404:
                    return None  # ID不存在
                elif response.status_code == 429:
                    # 频率限制，智能等待时间
                    # 根据重试次数调整等待时间：第一次5秒，第二次15秒，第三次60秒
                    wait_times = [5, 15, 60]
                    wait_time = wait_times[min(attempt, len(wait_times) - 1)]

                    logger.warning(f"⏳ API频率限制 (429)，等待 {wait_time} 秒... (尝试 {attempt + 1}/{MAX_RETRIES})")
                    logger.warning(f"🔍 当前请求间隔: {time.time() - self.last_request_time:.2f}秒")
                    time.sleep(wait_time)
                    continue
                else:
                    logger.warning(f"⚠️ API请求失败 (ID: {release_id}): HTTP {response.status_code}")
                    
            except requests.exceptions.Timeout:
                logger.warning(f"⏰ API请求超时 (ID: {release_id}), 尝试 {attempt + 1}/{MAX_RETRIES}")
            except requests.exceptions.RequestException as e:
                logger.warning(f"🌐 网络错误 (ID: {release_id}): {e}, 尝试 {attempt + 1}/{MAX_RETRIES}")
            
            if attempt < MAX_RETRIES - 1:
                wait_time = (2 ** attempt) * 5  # 指数退避
                time.sleep(wait_time)
        
        return False  # 表示请求失败

def connect_to_mongodb():
    """连接到MongoDB"""
    try:
        client = MongoClient(MONGO_URI)
        client.admin.command('ping')
        db = client[DB_NAME]
        logger.info(f"✅ 成功连接到MongoDB: {DB_NAME}")
        return client, db
    except Exception as e:
        logger.error(f"❌ MongoDB连接失败: {e}")
        raise

def load_404_ids():
    """加载404 ID列表到内存"""
    global not_found_ids_set
    if os.path.exists(NOT_FOUND_IDS_FILE):
        try:
            with open(NOT_FOUND_IDS_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                not_found_ids_set = set(data.get('not_found_ids', []))
                logger.info(f"📂 加载404 ID列表: {len(not_found_ids_set):,} 个ID")
                return len(not_found_ids_set)
        except Exception as e:
            logger.warning(f"⚠️ 加载404 ID文件失败: {e}")
            not_found_ids_set = set()
    else:
        logger.info("📂 404 ID文件不存在，从空集合开始")
        not_found_ids_set = set()
    return 0

def save_404_ids():
    """保存404 ID列表到文件"""
    global not_found_ids_set
    try:
        data = {
            'not_found_ids': sorted(list(not_found_ids_set)),
            'total_count': len(not_found_ids_set),
            'last_updated': datetime.now().isoformat()
        }
        with open(NOT_FOUND_IDS_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        logger.info(f"💾 已保存404 ID列表: {len(not_found_ids_set):,} 个ID")
    except Exception as e:
        logger.error(f"❌ 保存404 ID文件失败: {e}")

def add_404_id(release_id: int, save_immediately: bool = False):
    """添加404 ID到集合"""
    global not_found_ids_set
    if release_id not in not_found_ids_set:
        not_found_ids_set.add(release_id)
        logger.debug(f"📝 记录404 ID: {release_id}")

        if save_immediately:
            save_404_ids()

def get_404_ids_count() -> int:
    """获取404 ID数量"""
    global not_found_ids_set
    return len(not_found_ids_set)

def load_segment_progress():
    """加载分段检测进度"""
    if os.path.exists(SEGMENT_PROGRESS_FILE):
        try:
            with open(SEGMENT_PROGRESS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.warning(f"⚠️ 加载分段进度失败: {e}")
    return {'completed_segments': [], 'last_segment_start': START_ID}

def save_segment_progress(progress):
    """保存分段检测进度"""
    try:
        with open(SEGMENT_PROGRESS_FILE, 'w', encoding='utf-8') as f:
            json.dump(progress, f, indent=2, ensure_ascii=False)
    except Exception as e:
        logger.error(f"❌ 保存分段进度失败: {e}")

def detect_missing_ids_by_segments(db, start_id: int = START_ID, max_id: int = None) -> List[int]:
    """分段检测缺失的release ID"""
    logger.info("🔍 开始分段检测缺失的release ID...")
    start_time = time.time()

    collection = db[COLLECTION_NAME]

    # 获取数据库中的最大ID
    if max_id is None:
        try:
            max_id_doc = list(collection.find({}, {'id': 1}).sort('id', -1).limit(1))
            max_id = int(max_id_doc[0]['id']) if max_id_doc else 34419592
            logger.info(f"📊 数据库中最大ID: {max_id}")
        except Exception as e:
            logger.error(f"❌ 获取最大ID失败: {e}")
            max_id = 34419592  # 使用默认值

    # 加载进度
    progress = load_segment_progress()
    completed_segments = set(progress.get('completed_segments', []))

    # 加载已检测的缺失ID
    missing_ids = []
    if os.path.exists(MISSING_IDS_FILE):
        try:
            with open(MISSING_IDS_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                missing_ids = data.get('missing_ids', [])
                logger.info(f"📂 加载已检测的缺失ID: {len(missing_ids):,} 个")
        except Exception as e:
            logger.warning(f"⚠️ 加载缺失ID文件失败: {e}")

    # 计算段大小和段数
    total_ids = max_id - start_id + 1

    if MAX_SEGMENTS > 0:
        # 使用最大段数来计算每段大小
        total_segments = min(MAX_SEGMENTS, total_ids)
        segment_size = (total_ids + total_segments - 1) // total_segments  # 向上取整
        logger.info(f"📊 使用最大段数模式: {total_segments} 段, 每段约 {segment_size:,} 个ID")
    else:
        # 使用固定段大小
        segment_size = SEGMENT_SIZE
        total_segments = (total_ids + segment_size - 1) // segment_size
        logger.info(f"📊 使用固定段大小模式: {total_segments} 段, 每段 {segment_size:,} 个ID")

    logger.info(f"📊 总ID数量: {total_ids:,}, 分为 {total_segments} 段处理")

    # 分段检测
    for segment_idx in range(total_segments):
        segment_start = start_id + segment_idx * segment_size
        segment_end = min(segment_start + segment_size - 1, max_id)

        # 跳过已完成的段
        if segment_idx in completed_segments:
            logger.info(f"⏭️ 跳过已完成段 {segment_idx + 1}/{total_segments} (ID: {segment_start}-{segment_end})")
            continue

        logger.info(f"🔄 检测段 {segment_idx + 1}/{total_segments} (ID: {segment_start}-{segment_end})")

        try:
            # 获取该段的现有ID
            query = {'id': {'$gte': segment_start, '$lte': segment_end}}
            cursor = collection.find(query, {'id': 1, '_id': 0})
            existing_ids = {int(doc['id']) for doc in cursor}

            # 计算该段的缺失ID
            segment_all_ids = set(range(segment_start, segment_end + 1))
            segment_missing = segment_all_ids - existing_ids

            # 添加到总缺失ID列表
            missing_ids.extend(sorted(segment_missing))

            # 标记段为已完成
            completed_segments.add(segment_idx)

            logger.info(f"✅ 段 {segment_idx + 1} 完成: 缺失 {len(segment_missing):,} 个ID")

            # 保存进度
            progress['completed_segments'] = list(completed_segments)
            progress['last_segment_start'] = segment_start
            save_segment_progress(progress)

            # 定期保存缺失ID
            if segment_idx % 10 == 0 or segment_idx == total_segments - 1:
                save_missing_ids_to_file(missing_ids, max_id)

        except Exception as e:
            logger.error(f"❌ 检测段 {segment_idx + 1} 失败: {e}")
            continue

    # 排除已知的404 ID
    global not_found_ids_set
    original_count = len(missing_ids)
    missing_ids = [id for id in missing_ids if id not in not_found_ids_set]
    excluded_count = original_count - len(missing_ids)

    if excluded_count > 0:
        logger.info(f"🚫 排除已知404 ID: {excluded_count:,} 个")

    # 最终保存
    save_missing_ids_to_file(missing_ids, max_id)

    elapsed = time.time() - start_time
    logger.info(f"✅ 分段检测完成，耗时: {elapsed:.2f} 秒")
    logger.info(f"📊 总缺失ID数量: {len(missing_ids):,}")

    return missing_ids

def save_missing_ids_to_file(missing_ids: List[int], max_id: int):
    """保存缺失ID到文件"""
    try:
        data = {
            'missing_ids': missing_ids,
            'total_count': len(missing_ids),
            'max_id': max_id,
            'generated_at': datetime.now().isoformat(),
            'segment_size': SEGMENT_SIZE
        }
        with open(MISSING_IDS_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        logger.info(f"💾 已保存 {len(missing_ids):,} 个缺失ID到文件")
    except Exception as e:
        logger.error(f"❌ 保存缺失ID文件失败: {e}")

def find_missing_ids(db, start_id: int = START_ID, max_id: int = None) -> Set[int]:
    """查找缺失的release ID"""
    logger.info("🔍 开始检测缺失的release ID...")
    start_time = time.time()
    
    collection = db[COLLECTION_NAME]
    
    # 获取数据库中的最大ID
    if max_id is None:
        try:
            max_id_doc = list(collection.find({}, {'id': 1}).sort('id', -1).limit(1))
            max_id = max_id_doc[0]['id'] if max_id_doc else 34419592
            logger.info(f"📊 数据库中最大ID: {max_id}")
        except Exception as e:
            logger.error(f"❌ 获取最大ID失败: {e}")
            max_id = 34419592  # 使用默认值
    
    # 获取现有的ID集合
    logger.info(f"📥 获取现有ID集合 (范围: {start_id} - {max_id})...")
    existing_ids = set()
    
    # 使用批量查询优化性能
    batch_size = 10000
    for i in range(start_id, max_id + 1, batch_size):
        end_id = min(i + batch_size - 1, max_id)
        query = {'id': {'$gte': i, '$lte': end_id}}
        cursor = collection.find(query, {'id': 1, '_id': 0})
        
        batch_ids = {doc['id'] for doc in cursor}
        existing_ids.update(batch_ids)
        
        if len(existing_ids) % 100000 == 0:
            logger.info(f"🔄 已获取 {len(existing_ids):,} 个现有ID...")
    
    # 计算缺失的ID
    all_ids = set(range(start_id, max_id + 1))
    missing_ids = all_ids - existing_ids

    # 排除已知的404 ID
    global not_found_ids_set
    original_count = len(missing_ids)
    missing_ids = missing_ids - not_found_ids_set
    excluded_count = original_count - len(missing_ids)

    if excluded_count > 0:
        logger.info(f"🚫 排除已知404 ID: {excluded_count:,} 个")

    elapsed = time.time() - start_time
    logger.info(f"✅ 缺失ID检测完成，耗时: {elapsed:.2f} 秒")
    logger.info(f"📊 总ID范围: {len(all_ids):,}")
    logger.info(f"📊 现有ID数量: {len(existing_ids):,}")
    logger.info(f"📊 缺失ID数量: {len(missing_ids):,}")

    return missing_ids

def create_processing_batches(missing_ids: List[int]) -> List[Dict]:
    """将缺失ID分割成处理批次"""
    logger.info(f"📦 开始创建处理批次，总ID数: {len(missing_ids):,}")

    batches = []
    total_ids = len(missing_ids)

    for i in range(0, total_ids, PROCESSING_BATCH_SIZE):
        batch_end = min(i + PROCESSING_BATCH_SIZE, total_ids)
        batch_ids = missing_ids[i:batch_end]

        batch_info = {
            'batch_id': len(batches) + 1,
            'ids': batch_ids,
            'start_id': batch_ids[0],
            'end_id': batch_ids[-1],
            'count': len(batch_ids),
            'status': 'pending',
            'created_at': datetime.now().isoformat(),
            'progress': {
                'processed': 0,
                'successful': 0,
                'skipped_404': 0,
                'errors': 0
            }
        }
        batches.append(batch_info)

    logger.info(f"✅ 创建了 {len(batches)} 个批次，每批次最多 {PROCESSING_BATCH_SIZE} 个ID")

    # 保存批次配置
    save_batch_config(batches)

    return batches

def save_batch_config(batches: List[Dict]):
    """保存批次配置到文件"""
    try:
        config = {
            'batches': batches,
            'total_batches': len(batches),
            'batch_size': PROCESSING_BATCH_SIZE,
            'created_at': datetime.now().isoformat()
        }
        with open(BATCH_CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        logger.info(f"💾 已保存批次配置: {len(batches)} 个批次")
    except Exception as e:
        logger.error(f"❌ 保存批次配置失败: {e}")

def load_batch_config() -> List[Dict]:
    """加载批次配置"""
    if os.path.exists(BATCH_CONFIG_FILE):
        try:
            with open(BATCH_CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('batches', [])
        except Exception as e:
            logger.warning(f"⚠️ 加载批次配置失败: {e}")
    return []

def get_batch_progress_file(batch_id: int) -> str:
    """获取批次进度文件路径"""
    os.makedirs(BATCH_PROGRESS_DIR, exist_ok=True)
    return os.path.join(BATCH_PROGRESS_DIR, f'batch_{batch_id}_progress.json')

def save_batch_progress(batch_id: int, progress: Dict):
    """保存批次进度"""
    try:
        progress_file = get_batch_progress_file(batch_id)
        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress, f, indent=2, ensure_ascii=False)
    except Exception as e:
        logger.error(f"❌ 保存批次 {batch_id} 进度失败: {e}")

def load_batch_progress(batch_id: int) -> Dict:
    """加载批次进度"""
    progress_file = get_batch_progress_file(batch_id)
    if os.path.exists(progress_file):
        try:
            with open(progress_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.warning(f"⚠️ 加载批次 {batch_id} 进度失败: {e}")
    return {'processed': 0, 'successful': 0, 'skipped_404': 0, 'errors': 0, 'last_processed_id': None}

def get_release_table_by_id(db, release_id):
    """从release表中获取images字段"""
    try:
        # 将release_id转换为string类型进行查询
        release_id_str = str(release_id)
        release_doc = db.release.find_one({'id': release_id_str})
        if release_doc:
            return {
                'images': release_doc.get('images', [])
            }
        return {
            'images': []
        }
    except Exception as e:
        logger.error(f"获取release表数据失败 (release_id: {release_id}): {e}")
        return {
            'images': []
        }

def convert_api_response_to_document(api_data: Dict, y_id: str, db) -> Dict:
    """将API响应转换为数据库文档格式"""
    try:
        # 从release表获取images字段
        release_id = api_data.get('id')
        db_fields = get_release_table_by_id(db, release_id)
        # 基础字段
        doc = {
            'y_id': y_id,
            'id': api_data.get('id'),
            'title': api_data.get('title', ''),
            'country': api_data.get('country', ''),
            'master_id': api_data.get('master_id'),

            # 系统字段
            'images_permissions': 1,
            'permissions': 1,
            'source': 1,
            'created_at': datetime.now(timezone.utc),
            'updated_at': datetime.now(timezone.utc)
        }

        # 处理艺术家 (artists)
        artists = []
        if 'artists' in api_data:
            for artist in api_data['artists']:
                artist_doc = {
                    'artist_id': artist.get('id'),
                    'name': artist.get('name', ''),
                    'role': artist.get('role', 'Primary')
                }
                if 'anv' in artist:
                    artist_doc['anv'] = artist['anv']
                artists.append(artist_doc)
        doc['artists'] = artists

        # 处理额外艺术家 (extraartists)
        extra_artists = []
        if 'extraartists' in api_data:
            for artist in api_data['extraartists']:
                artist_doc = {
                    'artist_id': artist.get('id'),
                    'name': artist.get('name', ''),
                    'role': artist.get('role', 'Unknown')
                }
                if 'anv' in artist:
                    artist_doc['anv'] = artist['anv']
                extra_artists.append(artist_doc)
        doc['extra_artists'] = extra_artists

        # 处理标签 (labels)
        labels = []
        if 'labels' in api_data:
            for label in api_data['labels']:
                labels.append({
                    'name': label.get('name', ''),
                    'catno': label.get('catno', ''),
                    'id': str(label.get('id', ''))
                })
        doc['labels'] = labels

        # 处理公司 (companies)
        companies = []
        if 'companies' in api_data:
            for company in api_data['companies']:
                company_doc = {'name': company.get('name', '')}
                if 'id' in company:
                    company_doc['id'] = str(company['id'])
                if 'entity_type' in company:
                    company_doc['entity_type'] = company['entity_type']
                if 'entity_type_name' in company:
                    company_doc['entity_type_name'] = company['entity_type_name']
                if 'resource_url' in company:
                    company_doc['resource_url'] = company['resource_url']
                companies.append(company_doc)
        doc['companies'] = companies

        # 处理格式 (formats)
        formats = []
        if 'formats' in api_data:
            for format_item in api_data['formats']:
                format_doc = {
                    'name': format_item.get('name', ''),
                    'qty': str(format_item.get('qty', '')),
                    'text': format_item.get('text', ''),
                    'descriptions': format_item.get('descriptions', [])
                }
                formats.append(format_doc)
        doc['formats'] = formats

        # 处理简单列表字段
        doc['genres'] = api_data.get('genres', [])
        doc['styles'] = api_data.get('styles', [])

        # 处理标识符 (identifiers)
        identifiers = []
        if 'identifiers' in api_data:
            for identifier in api_data['identifiers']:
                identifiers.append({
                    'type': identifier.get('type', ''),
                    'value': identifier.get('value', ''),
                    'description': identifier.get('description', '')
                })
        doc['identifiers'] = identifiers

        # 处理曲目列表 (tracklist)
        tracklist = []
        if 'tracklist' in api_data:
            for track in api_data['tracklist']:
                track_doc = {
                    'position': track.get('position', ''),
                    'title': track.get('title', '')
                }
                if 'duration' in track:
                    track_doc['duration'] = track['duration']
                tracklist.append(track_doc)
        doc['tracklist'] = tracklist

        # 处理其他字段
        doc['images'] = db_fields['images']  # 从数据库获取
        doc['notes'] = api_data.get('notes', '')
        doc['year'] = api_data.get('year')

        return doc

    except Exception as e:
        logger.error(f"❌ 数据转换失败: {e}")
        return None

def format_array_for_csv(data_list):
    """将列表数据格式化为JSON数组格式，用于CSV存储"""
    if not data_list:
        return "[]"
    if isinstance(data_list, list):
        return json.dumps(data_list, ensure_ascii=False)
    return json.dumps([data_list], ensure_ascii=False)

def write_releases_to_csv(release_docs: List[Dict], csv_filename: str, append_mode: bool = True):
    """将release数据写入CSV文件"""
    if not release_docs:
        logger.info("没有数据需要写入CSV文件")
        return

    # 定义CSV字段（基于process_releases.py的字段结构）
    fieldnames = [
        'id', 'y_id', 'title', 'artists', 'extra_artists', 'labels', 'companies',
        'country', 'formats', 'genres', 'styles', 'identifiers', 'tracklist',
        'master_id', 'discogs_status', 'images', 'notes', 'year',
        'images_permissions', 'permissions', 'source', 'created_at', 'updated_at'
    ]

    try:
        # 检查文件是否存在以决定是否写入表头
        file_exists = os.path.exists(csv_filename)
        mode = 'a' if append_mode and file_exists else 'w'

        with open(csv_filename, mode, newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            # 只在新文件或覆盖模式时写入表头
            if not file_exists or not append_mode:
                writer.writeheader()

            # 写入数据
            for doc in release_docs:
                # 格式化数组字段
                csv_row = {}
                for field in fieldnames:
                    value = doc.get(field, '')

                    # 处理数组字段
                    if field in ['artists', 'extra_artists', 'labels', 'companies',
                               'formats', 'genres', 'styles', 'identifiers',
                               'tracklist', 'images']:
                        csv_row[field] = format_array_for_csv(value)
                    # 处理日期字段
                    elif field in ['created_at', 'updated_at'] and isinstance(value, datetime):
                        csv_row[field] = value.isoformat()
                    else:
                        csv_row[field] = value

                writer.writerow(csv_row)

        logger.info(f"✅ 成功写入 {len(release_docs)} 条记录到 {csv_filename}")

    except Exception as e:
        logger.error(f"❌ CSV写入失败: {e}")

def process_batch(batch_info: Dict, db, api_client: DiscogsAPIClient) -> Dict:
    """处理单个批次"""
    batch_id = batch_info['batch_id']
    batch_ids = batch_info['ids']

    logger.info(f"🚀 开始处理批次 {batch_id}: {len(batch_ids)} 个ID")
    logger.info(f"📊 ID范围: {batch_ids[0]} - {batch_ids[-1]}")

    # 加载批次进度
    progress = load_batch_progress(batch_id)
    last_processed_id = progress.get('last_processed_id')

    # 确定开始位置
    start_idx = 0
    if last_processed_id:
        try:
            start_idx = batch_ids.index(last_processed_id) + 1
            logger.info(f"📍 从ID {last_processed_id} 之后继续处理")
        except ValueError:
            logger.warning(f"⚠️ 未找到上次处理的ID {last_processed_id}，从头开始")

    # 获取当前最大的y_id计数器
    collection = db[COLLECTION_NAME]
    yid_counter = get_next_yid_counter(collection)

    batch_docs = []
    batch_csv_file = f'batch_{batch_id}_releases.csv'

    for i in range(start_idx, len(batch_ids)):
        if should_stop:
            logger.info(f"🛑 批次 {batch_id} 收到停止信号")
            break

        release_id = batch_ids[i]

        try:
            # API调用
            api_data = api_client.get_release(release_id)

            if api_data is None:
                # 404 - ID不存在
                add_404_id(release_id)  # 记录404 ID
                progress['skipped_404'] += 1
                logger.debug(f"⏭️ 批次 {batch_id} - ID {release_id} 不存在 (404)")

            elif api_data is False:
                # API请求失败
                progress['errors'] += 1
                logger.warning(f"❌ 批次 {batch_id} - ID {release_id} API请求失败")

            else:
                # 成功获取数据
                y_id = f"YRD{yid_counter}"
                doc = convert_api_response_to_document(api_data, y_id, db)

                if doc:
                    batch_docs.append(doc)
                    yid_counter += 1
                    progress['successful'] += 1
                    logger.debug(f"✅ 批次 {batch_id} - ID {release_id} 处理成功")

                    # 批量写入CSV
                    if len(batch_docs) >= BATCH_SIZE:
                        write_releases_to_csv(batch_docs, batch_csv_file, append_mode=True)
                        batch_docs = []
                else:
                    progress['errors'] += 1
                    logger.warning(f"❌ 批次 {batch_id} - ID {release_id} 数据转换失败")

            progress['processed'] += 1
            progress['last_processed_id'] = release_id

            # 定期保存进度
            if progress['processed'] % 100 == 0:
                save_batch_progress(batch_id, progress)
                logger.info(f"📊 批次 {batch_id} 进度: {progress['processed']}/{len(batch_ids)} "
                          f"(成功: {progress['successful']}, 404: {progress['skipped_404']}, 错误: {progress['errors']})")

        except Exception as e:
            progress['errors'] += 1
            logger.error(f"❌ 批次 {batch_id} - ID {release_id} 处理异常: {e}")

    # 写入剩余的文档到CSV
    if batch_docs:
        write_releases_to_csv(batch_docs, batch_csv_file, append_mode=True)

    # 保存最终进度
    progress['completed_at'] = datetime.now().isoformat()
    save_batch_progress(batch_id, progress)

    logger.info(f"✅ 批次 {batch_id} 处理完成")
    logger.info(f"📊 最终统计: 处理 {progress['processed']}, 成功 {progress['successful']}, "
              f"404跳过 {progress['skipped_404']}, 错误 {progress['errors']}")

    return progress

def process_missing_releases(missing_ids: List[int], db, api_client: DiscogsAPIClient, progress: ProgressTracker):
    """处理缺失的release记录"""
    collection = db[COLLECTION_NAME]
    batch_docs = []

    # 获取当前最大的y_id计数器
    yid_counter = get_next_yid_counter(collection)

    for release_id in missing_ids:
        if should_stop:
            logger.info("🛑 接收到停止信号，正在保存当前批次...")
            break

        try:
            # 调用API获取数据
            api_data = api_client.get_release(release_id)

            if api_data is None:
                # 404 - ID不存在
                add_404_id(release_id)  # 记录404 ID
                progress.update('404', release_id)
                logger.debug(f"⏭️ ID {release_id} 不存在，跳过")
                continue
            elif api_data is False:
                # API请求失败
                progress.update('error', release_id)
                logger.error(f"❌ API请求失败: ID {release_id}")
                continue

            # 转换为数据库文档
            y_id = f"YRD{yid_counter}"
            doc = convert_api_response_to_document(api_data, y_id, db)

            if doc is None:
                progress.update('error', release_id)
                continue

            batch_docs.append(doc)
            yid_counter += 1
            progress.update('success', release_id)

            # 批量写入CSV
            if len(batch_docs) >= BATCH_SIZE:
                write_releases_to_csv(batch_docs, CSV_OUTPUT_FILE)
                batch_docs = []

            # 显示进度
            if progress.processed % 100 == 0:
                stats = progress.get_stats()
                logger.info(f"🔄 进度: {progress.processed:,}/{len(missing_ids):,} "
                          f"(成功: {progress.successful:,}, 404: {progress.skipped_404:,}, "
                          f"错误: {progress.errors:,}, 速度: {stats['processing_rate']:.2f}/秒)")

        except Exception as e:
            logger.error(f"❌ 处理ID {release_id} 时发生错误: {e}")
            progress.update('error', release_id)

    # 写入剩余的文档到CSV
    if batch_docs:
        write_releases_to_csv(batch_docs, CSV_OUTPUT_FILE)

    # 保存最终进度
    progress.save_progress()

def get_next_yid_counter(collection) -> int:
    """获取下一个y_id计数器"""
    try:
        pipeline = [
            {"$match": {"y_id": {"$regex": "^YRD\\d+$"}}},
            {"$addFields": {
                "y_id_num": {"$toInt": {"$substr": ["$y_id", 3, -1]}}
            }},
            {"$sort": {"y_id_num": -1}},
            {"$limit": 1}
        ]

        result = list(collection.aggregate(pipeline))
        if result:
            max_num = result[0]['y_id_num']
            logger.info(f"📊 数据库中最大YRD编号: YRD{max_num}，新记录从 YRD{max_num + 1} 开始")
            return max_num + 1
        else:
            logger.info("📊 数据库中无YRD记录，从 YRD1 开始")
            return 1
    except Exception as e:
        logger.error(f"❌ 获取y_id计数器失败: {e}")
        return 1

def insert_batch(collection, docs: List[Dict]):
    """批量插入文档"""
    try:
        if docs:
            result = collection.insert_many(docs)
            logger.debug(f"✅ 批量插入 {len(result.inserted_ids)} 条记录")
    except Exception as e:
        logger.error(f"❌ 批量插入失败: {e}")
        # 尝试逐条插入
        for doc in docs:
            try:
                collection.insert_one(doc)
            except Exception as single_error:
                logger.error(f"❌ 单条插入失败 (ID: {doc.get('id')}): {single_error}")

def main_staged():
    """分阶段处理主函数"""
    logger.info("🚀 Discogs API Release 数据补全器启动 (分阶段处理模式)")
    logger.info("=" * 60)
    if MAX_SEGMENTS > 0:
        logger.info(f"📊 最大段数: {MAX_SEGMENTS} (动态计算每段大小)")
    else:
        logger.info(f"📊 固定段大小: {SEGMENT_SIZE:,}")
    logger.info(f"📦 批次大小: {PROCESSING_BATCH_SIZE:,}")
    logger.info(f"📁 CSV输出目录: {os.getcwd()}")

    if TEST_MODE:
        logger.info("🧪 测试模式已启用")

    try:
        # 加载404 ID列表
        initial_404_count = load_404_ids()
        logger.info(f"📊 已加载404 ID列表: {initial_404_count:,} 个")

        # 连接数据库
        client, db = connect_to_mongodb()

        # 阶段1: 检测缺失ID
        logger.info("\n" + "=" * 60)
        logger.info("📋 阶段1: 检测缺失的release ID")
        logger.info("=" * 60)

        missing_ids = detect_missing_ids_by_segments(db)

        if not missing_ids:
            logger.info("✅ 没有发现缺失的ID，数据库已完整")
            return

        # 测试模式限制
        if TEST_MODE:
            missing_ids = missing_ids[:50]  # 测试模式只处理50个ID
            logger.info(f"🧪 测试模式：只处理前 {len(missing_ids)} 个ID")

        # 阶段2: 创建处理批次
        logger.info("\n" + "=" * 60)
        logger.info("📦 阶段2: 创建处理批次")
        logger.info("=" * 60)

        batches = create_processing_batches(missing_ids)

        # 阶段3: 分批处理
        logger.info("\n" + "=" * 60)
        logger.info("🔄 阶段3: 分批处理API调用")
        logger.info("=" * 60)

        api_client = DiscogsAPIClient()
        total_stats = {'processed': 0, 'successful': 0, 'skipped_404': 0, 'errors': 0}

        for batch_info in batches:
            if should_stop:
                logger.info("🛑 收到停止信号，终止处理")
                break

            batch_stats = process_batch(batch_info, db, api_client)

            # 累计统计
            for key in total_stats:
                total_stats[key] += batch_stats.get(key, 0)

            logger.info(f"📊 累计进度: 处理 {total_stats['processed']:,}, "
                      f"成功 {total_stats['successful']:,}, "
                      f"404跳过 {total_stats['skipped_404']:,}, "
                      f"错误 {total_stats['errors']:,}")

        # 输出最终统计
        logger.info("\n" + "=" * 60)
        logger.info("📊 最终统计报告")
        logger.info("=" * 60)
        logger.info(f"总处理数量: {total_stats['processed']:,}")
        logger.info(f"成功获取: {total_stats['successful']:,}")
        logger.info(f"404跳过: {total_stats['skipped_404']:,}")
        logger.info(f"错误数量: {total_stats['errors']:,}")

        if total_stats['processed'] > 0:
            success_rate = (total_stats['successful'] / total_stats['processed']) * 100
            logger.info(f"成功率: {success_rate:.2f}%")

        logger.info("=" * 60)
        logger.info(f"📊 最终404 ID数量: {get_404_ids_count():,}")

        client.close()

    except KeyboardInterrupt:
        logger.info("🛑 用户中断程序")
    except Exception as e:
        logger.error(f"❌ 处理过程中发生错误: {e}")
    finally:
        # 保存404 ID列表
        save_404_ids()
        logger.info("🏁 程序结束")

def main():
    """主函数"""
    logger.info("🚀 Discogs API Release 数据补全器启动 (CSV输出模式)")
    logger.info("=" * 60)
    logger.info(f"📁 CSV输出文件: {CSV_OUTPUT_FILE}")
    
    if TEST_MODE:
        logger.info("🧪 测试模式已启用")
    
    try:
        # 加载404 ID列表
        initial_404_count = load_404_ids()
        logger.info(f"📊 已加载404 ID列表: {initial_404_count:,} 个")

        # 连接数据库
        client, db = connect_to_mongodb()

        # 初始化组件
        progress = ProgressTracker()
        api_client = DiscogsAPIClient()
        
        # 查找缺失的ID
        missing_ids = find_missing_ids(db, progress.last_id)
        
        if not missing_ids:
            logger.info("✅ 没有发现缺失的ID，数据库已完整")
            return
        
        # 转换为排序列表
        missing_ids_list = sorted(list(missing_ids))
        
        # 测试模式只处理前几条
        if TEST_MODE:
            missing_ids_list = missing_ids_list[:10]
            logger.info(f"🧪 测试模式：只处理前 {len(missing_ids_list)} 条记录")
        
        # 限制处理数量
        if MAX_RECORDS > 0:
            missing_ids_list = missing_ids_list[:MAX_RECORDS]
            logger.info(f"📝 限制处理记录数: {len(missing_ids_list)}")
        
        logger.info(f"🎯 开始处理 {len(missing_ids_list):,} 个缺失的ID...")

        # 处理缺失的ID
        process_missing_releases(missing_ids_list, db, api_client, progress)

        # 输出最终统计
        final_stats = progress.get_stats()
        logger.info("\n" + "=" * 60)
        logger.info("📊 最终统计报告")
        logger.info("=" * 60)
        logger.info(f"总处理数量: {final_stats['processed']:,}")
        logger.info(f"成功获取: {final_stats['successful']:,}")
        logger.info(f"404跳过: {final_stats['skipped_404']:,}")
        logger.info(f"错误数量: {final_stats['errors']:,}")
        logger.info(f"成功率: {final_stats['success_rate']:.2f}%")
        logger.info(f"处理速度: {final_stats['processing_rate']:.2f} 条/秒")
        logger.info(f"总耗时: {final_stats['elapsed_time']:.2f} 秒")
        logger.info(f"📊 最终404 ID数量: {get_404_ids_count():,}")
        logger.info("=" * 60)

    except KeyboardInterrupt:
        logger.info("⏹️ 用户中断处理")
    except Exception as e:
        logger.error(f"❌ 处理过程中发生错误: {e}")
        raise
    finally:
        # 保存404 ID列表
        save_404_ids()
        if 'client' in locals():
            client.close()
        logger.info("🏁 程序结束")

if __name__ == "__main__":
    # 检查是否使用分阶段处理模式
    staged_mode = os.getenv('STAGED_MODE', 'true').lower() == 'true'

    if staged_mode:
        main_staged()
    else:
        main()
